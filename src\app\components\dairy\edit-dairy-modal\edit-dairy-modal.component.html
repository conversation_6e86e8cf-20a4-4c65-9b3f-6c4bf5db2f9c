<div class="modal-overlay">
  <div class="modal-container">
    <div class="modal-header">
      <h2>Edit Dairy Animal</h2>
      <button class="close-btn" (click)="closeModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-body">
      <form [formGroup]="dairyForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <div class="form-group">
            <label for="code">Code</label>
            <input
              type="text"
              id="code"
              formControlName="code"
              placeholder="Enter animal code"
            >
            <div class="error-message" *ngIf="dairyForm.get('code')?.invalid && dairyForm.get('code')?.touched">
              Code is required
            </div>
          </div>

          <div class="form-group">
            <label for="animalType">Type</label>
            <select
              id="animalType"
              formControlName="animalType"
              class="scrollable-select"
              size="3"
            >
              <option *ngFor="let type of animalTypes" [value]="type">{{ type }}</option>
            </select>
            <div class="error-message" *ngIf="dairyForm.get('animalType')?.invalid && dairyForm.get('animalType')?.touched">
              Type is required
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="noFamily">Family Number</label>
            <input
              type="text"
              id="noFamily"
              formControlName="noFamily"
              placeholder="Enter family number"
            >
            <div class="error-message" *ngIf="dairyForm.get('noFamily')?.invalid && dairyForm.get('noFamily')?.touched">
              Family number is required
            </div>
          </div>

          <div class="form-group">
            <label for="weight">Weight (kg)</label>
            <input
              type="number"
              id="weight"
              formControlName="weight"
              placeholder="Enter weight"
              step="0.1"
            >
            <div class="error-message" *ngIf="dairyForm.get('weight')?.invalid && dairyForm.get('weight')?.touched">
              <span *ngIf="dairyForm.get('weight')?.errors?.['required']">Weight is required</span>
              <span *ngIf="dairyForm.get('weight')?.errors?.['min']">Weight must be positive</span>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="weightDate">Weight Date</label>
            <input
              type="date"
              id="weightDate"
              formControlName="weightDate"
            >
            <div class="error-message" *ngIf="dairyForm.get('weightDate')?.invalid && dairyForm.get('weightDate')?.touched">
              Weight date is required
            </div>
          </div>

          <div class="form-group">
            <label for="dateFertilization">Fertilization Date</label>
            <input
              type="date"
              id="dateFertilization"
              formControlName="dateFertilization"
            >
            <div class="error-message" *ngIf="dairyForm.get('dateFertilization')?.invalid && dairyForm.get('dateFertilization')?.touched">
              Fertilization date is required
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="expectedDate">Expected Birth Date</label>
          <input
            type="date"
            id="expectedDate"
            formControlName="expectedDate"
            readonly
          >
          <div class="hint-text">Automatically calculated (9 months after fertilization)</div>
          <div class="error-message" *ngIf="dairyForm.get('expectedDate')?.invalid && dairyForm.get('expectedDate')?.touched">
            Expected date is required
          </div>
        </div>

        <div class="milk-records-summary" *ngIf="dairy && dairy.milk && dairy.milk.length > 0">
          <h4>Milk Records</h4>
          <p>This animal has {{ dairy.milk.length }} milk records.</p>
        </div>

        <div class="error-message main-error" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>

        <div class="form-actions">
          <button type="button" class="cancel-btn" (click)="closeModal()">Cancel</button>
          <button type="submit" class="submit-btn" [disabled]="isLoading">
            <i class="fas fa-spinner fa-spin" *ngIf="isLoading"></i>
            {{ isLoading ? 'Saving...' : 'Update Dairy Animal' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
