<main class="content-container">
  <div class="frame-4">
    <!-- Make New Feed Button -->
    <button class="frame-9" (click)="openMakeNewFeedModal()">
      <img
        class="btn-icon"
        src="assets/images/icons/feed.png"
        alt="Add Feed Icon"
      />
      <div class="btntext">Make New Feed</div>
    </button>

    <!-- Edit Existed Feed Button -->
    <button class="frame-9" (click)="openEditExistFeedModal()">
      <img
        class="btn-icon"
        src="assets/images/icons/iconupdate.png"
        alt="Edit Feed Icon"
      />
      <div class="btntext">Edit Existed Feed</div>
    </button>

    <!-- Search Input -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <img
          class="search-icon"
          src="assets/images/icons/search.png"
          alt="Search Icon"
        />
        <input
          type="text"
          class="search-field"
          placeholder="Search"
          [(ngModel)]="searchTerm"
          (input)="performSearch()"
        />
      </div>
    </div>

    <!-- Filter Button -->
    <div
      class="filter-dropdown-container"
      [class.active]="isFilterDropdownOpen"
    >
      <button class="frame-9 filter-button" (click)="toggleFilterDropdown()">
        <img class="double-left1" src="assets/images/icons/filter.png" />
        <div class="btntext">Filter</div>
      </button>
      <div class="filter-dropdown-menu">
        <div class="filter-option" (click)="applyFilter('feedName')">
          Feed Name
        </div>
        <div class="filter-option" (click)="applyFilter('category')">
          Category
        </div>
      </div>
    </div>

    <!-- Delete Feed Button -->
    <button class="frame-9 delete-btn" (click)="openDeleteFeedModal()">
      <img
        class="btn-icon"
        src="assets/images/icons/Trash.png"
        alt="Delete Icon"
      />
      <div class="btntext">Delete Feed</div>
    </button>
  </div>

  <!-- Feed Table -->
  <div class="table-container">
    <h3>Feed</h3>
    <table>
      <thead>
        <tr>
          <th><input type="checkbox" /></th>
          <th>Count</th>
          <th>Name</th>
          <th>Category</th>
          <th>Protein Percentage</th>
          <th>TDN Percentage</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let feed of filteredFeeds">
          <td><input type="checkbox" /></td>
          <td>{{ feed.count }}</td>
          <td>{{ feed.feedName }}</td>
          <td>{{ feed.category }}</td>
          <td>{{ feed.proteinPercentage }}%</td>
          <td>{{ feed.tdnPercentage }}%</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Make New Feed Modal -->
  <div class="choose-update-milch" *ngIf="showMakeNewFeedModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeAllModals()">
        <img src="assets/images/icons/cross.png" alt="Close" />
      </button>
      <div class="frame-5">
        <img
          class="modal-icon"
          src="assets/images/icons/feed.png"
          alt="Make New Feed Icon"
        />
        <div class="text-wrapper-5">Make New Feed</div>
      </div>
      <div class="frame-6">
        <!-- First row: Feed Name and Category -->
        <div class="form-grid">
          <div class="form-group">
            <label class="text-wrapper-6">Feed Name</label>
            <input
              type="text"
              class="data-filled"
              placeholder="Feed Name"
              [(ngModel)]="selectedFeed.feedName"
            />
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Category</label>
            <select class="data-filled" [(ngModel)]="selectedFeed.category">
              <option value="">Select Category</option>
              <option value="category1">Category 1</option>
              <option value="category2">Category 2</option>
              <option value="category3">Category 3</option>
            </select>
          </div>
        </div>

        <!-- Second row: Type, Ingredients, Price, and Add button -->
        <div class="form-flex dropdowns-row">
          <div class="form-group">
            <label class="text-wrapper-6">Type</label>
            <select class="data-filled" [(ngModel)]="selectedFeed.type">
              <option value="">Select Type</option>
              <option value="type1">Type 1</option>
              <option value="type2">Type 2</option>
              <option value="type3">Type 3</option>
            </select>
          </div>
          <div class="form-group">
            <label class="text-wrapper-6">Ingredients</label>
            <select class="data-filled" [(ngModel)]="selectedFeed.ingredients">
              <option value="">Component</option>
              <option value="component1">Component 1</option>
              <option value="component2">Component 2</option>
              <option value="component3">Component 3</option>
            </select>
          </div>
          <div class="form-group price-group">
            <label class="text-wrapper-6">Price</label>
            <input
              type="number"
              class="data-filled price-input"
              [(ngModel)]="selectedFeed.price"
              placeholder="0.00"
            />
          </div>
          <div class="form-group add-button-container">
            <button class="add-button" (click)="addSelectedItem()">
              <img
                src="assets/images/icons/addingred.png"
                alt="Add"
                class="add-icon"
              />
            </button>
          </div>
        </div>

        <div class="selected-items-container">
          
            <table>
              <tr class="m">
                <th>type</th>
                <th>ingredients</th>
                <th>price</th>
                <th></th>
              </tr>
              
            
               <tr *ngFor="let item of selectedItems" class="selected-item">
                <td>{{ item.type }}</td>
                <td>{{ item.ingredients }}</td>
                <td>{{ item.price }}</td>
                <td>   <button class="remove-button" (click)="removeItem(item)"><img src="assets/images/icons/delingred.png"></button></td>
              </tr>
            
           
            </table>

         
          </div>
        </div>

        <div class="frame-8">
          <button class="frame-9 modal-save-btn" (click)="submitNewFeed()">
            <img
              class="btn-icon"
              src="assets/images/icons/save.png"
              alt="Save Feed Icon"
            />
            <div class="btntext">Save Feed</div>
          </button>
        </div>
      </div>
    </div>
 
  <!-- Edit Exist Feed Modal -->
  <div class="choose-update-milch" *ngIf="showEditExistFeedModal">
    <div class="frame-4 modal add-edit-modal">
      <button class="close-btn" (click)="closeAllModals()">
        <img src="assets/images/icons/cross.png" alt="Close" />
      </button>
      <div class="frame-5">
        <img
          class="modal-icon"
          src="assets/images/icons/update.png"
          alt="Edit Feed Icon"
        />
        <div class="text-wrapper-5">Edit Exist Feed</div>
      </div>
      <div class="frame-6">
        <div class="form-grid">
          <div class="form-group">
            <input
              type="text"
              class="data-filledd"
              placeholder="Enter Feed Name"
              [(ngModel)]="selectedFeed.feedName"
            />
          </div>
          <div class="form-group show-button-container">
            <button class="show-data-btn" (click)="showDataForFeed()">
              <div class="btntext">Show</div>
            </button>
          </div>
        </div>

        <div class="feed-data-container" *ngIf="isFeedDataLoaded">
          <div class="form-grid dropdowns-row edit-dropdowns-row">
            <div class="form-group">
              <label class="text-wrapper-6">Category</label>
              <select class="data-filled" [(ngModel)]="selectedFeed.category">
                <option value="">Select Category</option>
                <option value="category1">Category 1</option>
                <option value="category2">Category 2</option>
                <option value="category3">Category 3</option>
              </select>
            </div>
            <div class="form-group">
              <label class="text-wrapper-6">Type</label>
              <select class="data-filled" [(ngModel)]="selectedFeed.type">
                <option value="">Select Type</option>
                <option value="type1">Type 1</option>
                <option value="type2">Type 2</option>
                <option value="type3">Type 3</option>
              </select>
            </div>
            <div class="form-group">
              <label class="text-wrapper-6">Ingredients</label>
              <select
                class="data-filled"
                [(ngModel)]="selectedFeed.ingredients"
              >
                <option value="">Select Component</option>
                <option value="component1">Component 1</option>
                <option value="component2">Component 2</option>
                <option value="component3">Component 3</option>
              </select>
            </div>
            <div class="form-group price-group">
              <label class="text-wrapper-6">Price</label>
              <input
                type="number"
                class="data-filled price-input"
                [(ngModel)]="selectedFeed.price"
                placeholder="0.00"
              />
            </div>
            <div class="form-group add-button-container">
              <button class="add-button" (click)="addSelectedItem()">
                <img src="assets/icons/add.svg" alt="Add" class="add-icon" />
              </button>
            </div>
          </div>

          <div class="selected-items-container">
            <div *ngFor="let item of selectedItems" class="selected-item">
              <span
                >{{ item.type }} - {{ item.ingredients }} -
                {{ item.price }}</span
              >
              <button class="remove-button" (click)="removeItem(item)">
                ×
              </button>
            </div>
          </div>
        </div>

        <div class="frame-8" *ngIf="isFeedDataLoaded">
          <button class="frame-9 modal-save-btn" (click)="submitUpdateFeed()">
            <img
              class="btn-icon"
              src="assets/images/icons/save.png"
              alt="Save Icon"
            />
            <div class="btntext">Save Updates</div>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="choose-update-milch" *ngIf="showDeleteFeedModal">
    <div class="frame-4 modaldelete">
      <div class="delete-content">
        <img class="delete-icon" src="assets/images/icons/delete.png" alt="" />
        <div class="delete-text">Delete Feed?</div>
        <div class="delete-buttons">
          <button class="delete-cancel-btn" (click)="closeAllModals()">
            Cancel
          </button>
          <button class="delete-confirm-btn" (click)="confirmDeleteFeed()">
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</main>
