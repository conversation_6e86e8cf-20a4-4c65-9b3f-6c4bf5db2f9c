.custom-dropdown {
  width: 100%;
  position: relative;
  font-family: inherit;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.dropdown-container {
  position: relative;
  width: 100%;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 42px;
}

.dropdown-header:hover {
  border-color: #aaa;
}

.dropdown-header:focus, .dropdown-header:active {
  outline: none;
  border-color: #8bc34a;
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.2);
}

.selected-option {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #333;
}

.placeholder {
  color: #999;
  font-style: italic;
}

.dropdown-actions {
  display: flex;
  align-items: center;
}

.clear-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 0;
  margin-right: 8px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.clear-btn:hover {
  color: #ff5252;
}

.dropdown-arrow {
  color: #666;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.2s ease;
}

.search-container {
  padding: 10px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 32px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #8bc34a;
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.1);
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
}

.options-container {
  overflow-y: auto;
  max-height: 240px;
  padding: 5px 0;
}

.option-item {
  padding: 10px 15px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.2s ease;
}

.option-item:hover {
  background-color: #f5f8fa;
}

.option-item.selected {
  background-color: rgba(139, 195, 74, 0.1);
  color: #8bc34a;
  font-weight: 500;
}

.check-icon {
  color: #8bc34a;
  font-size: 12px;
}

.no-results {
  padding: 15px;
  text-align: center;
  color: #999;
  font-style: italic;
  font-size: 14px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}