import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

export interface Dairy {
  id?: number;
  code: string;
  type?: string;
  herdNumber?: number | null;
  milkProduction?: number | null;
  fatPercentage?: number | null;
  weight?: number | null;
  weightDate?: string;
  statueFortification?: string;
  dateOfArtificialInsemination?: string;
  expectedDateOfCalving?: string;
  healthcare?: string;
  gender?: string;
  dateOfBirth?: string;
  madeArtificialInsemination?: string;
  takenVaccinations?: string;
  date: string;
  morningQuantity: number;
  eveningQuantity: number;
  totalQuantity: number;
  notes: string;
}

@Injectable({
  providedIn: 'root'
})
export class DairyService {
  private apiUrl = 'https://sra.runasp.net/api'; // Adjust as per your actual API endpoint

  constructor(private http: HttpClient) { }

  getAllAnimals(): Observable<Dairy[]> {
     return this.http.get<Dairy[]>(`${this.apiUrl}/MilkCollections`).pipe(
       catchError(error => {
         console.error('Error fetching animals', error);
         return of([]);
       })
     );
   }
  getDairyRecordById(id: number): Observable<Dairy> {
    return this.http.get<Dairy>(`${this.apiUrl}/MilkCollections${id}`).pipe(
      tap(() => console.log(`Fetched dairy record id=${id}`)),
      catchError(this.handleError<Dairy>(`getDairyRecordById id=${id}`))
    );
  }

  createDairyRecord(dairy: Dairy): Observable<Dairy> {
    return this.http.post<Dairy>(this.apiUrl, dairy).pipe(
      tap((newRecord: Dairy) => console.log('Added dairy record', newRecord)),
      catchError(this.handleError<Dairy>('createDairyRecord'))
    );
  }

  updateDairyRecord(dairy: Dairy): Observable<any> {
    return this.http.put(`${this.apiUrl}/MilkCollections${dairy.id}`, dairy).pipe(
      tap(() => console.log('Updated dairy record', dairy)),
      catchError(this.handleError<any>('updateDairyRecord'))
    );
  }

  deleteDairyRecord(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/MilkCollections${id}`).pipe(
      tap(() => console.log(`Deleted dairy record id=${id}`)),
      catchError(this.handleError<any>('deleteDairyRecord'))
    );
  }

  initializeDairy(): Dairy {
    return {
      code: '',
      type: '',
      herdNumber: null,
      milkProduction: null,
      fatPercentage: null,
      weight: null,
      weightDate: '',
      statueFortification: '',
      dateOfArtificialInsemination: '',
      expectedDateOfCalving: '',
      healthcare: '',
      gender: '',
      dateOfBirth: '',
      madeArtificialInsemination: '',
      takenVaccinations: '',
      date: '',
      morningQuantity: 0,
      eveningQuantity: 0,
      totalQuantity: 0,
      notes: ''
    };
  }

  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(error);
      console.log(`${operation} failed: ${error.message}`);
      return of(result as T);
    };
  }
} 