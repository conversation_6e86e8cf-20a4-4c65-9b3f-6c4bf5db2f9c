// Test file to verify Animal update functionality
// This file can be used to test the updateAnimal method

import { HttpClient } from '@angular/common/http';
import { Animal } from './services/animal.service';

export class TestAnimalUpdate {
  private apiUrl = 'https://sra.runasp.net/api';

  constructor(private http: HttpClient) {}

  testUpdateAnimal() {
    // Sample animal data for testing
    const testAnimal: Animal = {
      id: 1, // Make sure this ID exists in your database
      code: 'TEST001',
      herdNumber: 'H001',
      animalType: 'Dairy',
      weight: 500,
      weightDate: '2024-01-15',
      dateOfBirth: '2022-01-01',
      healthcareNote: 'Updated test note',
      takenVaccinations: 'Updated vaccinations',
      madeArtificialInsemination: true,
      dateOfArtificialInsemination: '2024-01-10',
      statueOfInsemination: 'Pregnant',
      expectedDateOfCalving: '2024-10-10',
      gender: 'Female'
    };

    console.log('Testing animal update with data:', testAnimal);
    console.log('API URL:', `${this.apiUrl}/Animal/${testAnimal.id}`);

    // Test the HTTP PUT request
    this.http.put<Animal>(`${this.apiUrl}/Animal/${testAnimal.id}`, testAnimal)
      .subscribe({
        next: (response) => {
          console.log('✅ Update successful:', response);
        },
        error: (error) => {
          console.error('❌ Update failed:', error);
          console.error('Error details:', {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            error: error.error
          });
        }
      });
  }
}
