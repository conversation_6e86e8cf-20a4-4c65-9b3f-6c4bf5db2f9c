
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Apply Roboto font to all elements in the dashboard */
*{
  font-family: 'Roboto', sans-serif;
}


.dashboard-content-container {
  padding: 20px;
  background-color: #f8f8f8;
  min-height: calc(100vh - 60px); /* Adjust based on navbar height */
  box-sizing: border-box;
}

.dashboard-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.print-button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px; /* Add some space above the button */
  padding-bottom: 20px; /* Add some padding below if needed */
  width: 100%; /* Ensure it takes full width to center effectively */
}

.print-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px; /* Slightly more padding */
  border-radius: 25px; /* More rounded */
  background-color: #4CAF50; /* Green color for print button */
  color: #ffffff;
  font-size: 15px; /* Slightly larger font */
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
}

.print-button:hover {
  background-color: #45a049;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px); /* More pronounced lift */
}

.print-button img {
  width: 20px; /* Slightly larger icon */
  height: 20px;
  filter: invert(100%); /* Make icon white */
}

.report-sections-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Exactly two columns for larger screens */
  gap: 20px;
}

.report-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  display: flex;
  flex-direction: column;
  max-height: 300px; /* Re-introduce max-height for truncation */
  overflow-y: auto; /* Re-enable scrolling for individual cards */
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: #a0a0a0 #f1f1f1; /* Darker scrollbar thumb */
  transition: all 0.3s ease;
}

/* Webkit scrollbar styles */
.report-card::-webkit-scrollbar {
  width: 8px;
}

.report-card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.report-card::-webkit-scrollbar-thumb {
  background: #a0a0a0;
  border-radius: 10px;
}

.report-card::-webkit-scrollbar-thumb:hover {
  background: #777;
}

.report-card:hover {
  transform: translateY(-5px); /* Lift effect on hover */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.report-card-header {
  display: flex;
  align-items: center;
  gap: 12px; /* Slightly more gap */
  margin-bottom: 20px; /* More space below header */
  padding-bottom: 15px; /* More padding below line */
  border-bottom: 1px solid #e0e0e0; /* Slightly more prominent border */
}

.report-card-icon {
  width: 32px; /* Slightly larger icon */
  height: 32px;
}

.report-card-title {
  color: #222; /* Darker text for prominence */
  font-size: 20px; /* Larger title */
  font-weight: 700; /* Bolder title */
  margin: 0;
}

.report-card-items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.report-item {
  font-size: 15px; /* Slightly larger text */
  color: #444; /* Slightly darker text */
  padding: 10px 0; /* More vertical padding */
  border-bottom: 1px dashed #e9e9e9; /* Lighter dashed border */
}

.report-item:last-child {
  border-bottom: none;
  padding-bottom: 0; /* Remove bottom padding for last item */
}

@media (max-width: 768px) {
  .report-sections-grid {
    grid-template-columns: 1fr;
  }
  .report-card {
    max-height: unset; /* Ensure max-height is unset on smaller screens too */
  }
  .print-button {
    width: 100%; /* Ensure it remains full width on small screens */
    justify-content: center;
  }
}
