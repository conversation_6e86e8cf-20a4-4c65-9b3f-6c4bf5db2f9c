<div class="modal-overlay">
  <div class="choose-update-milch">
    <div class="frame-4">
      <div class="frame-5">
        <img class="update" src="icons/update.png" alt="Update Icon">
        <div class="text-wrapper-5">Add Dairy Animal</div>
      </div>

      <div class="frame-6">
        <form [formGroup]="dairyForm" (ngSubmit)="onSubmit()">
          <div class="row">
            <div class="frame-7">
              <div class="text-wrapper-6">Code</div>
              <input class="data-filled" placeholder="Code" id="code" formControlName="code">
            </div>

            <div class="frame-7">
              <div class="text-wrapper-6">Type</div>
              <div class="frame-11">
                <div class="raddiv">
                  <input type="radio" name="animalType" id="milch" class="radio" value="Dairy" formControlName="animalType">
                  <label for="milch">Dairy</label>
                </div>
                <div class="raddiv">
                  <input type="radio" name="animalType" id="drying" class="radio" value="Drying" formControlName="animalType">
                  <label for="drying">Drying</label>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="frame-7">
              <div class="text-wrapper-6">Date of Birth</div>
              <input class="data-filled" type="date" id="dateBirth" formControlName="dateBirth">
            </div>

            <div class="frame-7">
              <div class="text-wrapper-6">Herd Number</div>
              <input class="data-filled" placeholder="Herd Number" id="noFamily" formControlName="noFamily">
            </div>
          </div>

          <div class="row">
            <div class="frame-7">
              <div class="text-wrapper-6">Made Artificial Insemination</div>
              <div class="frame-11">
                <div class="raddiv">
                  <input type="radio" name="madeArtificialInsemination" id="yes" class="radio" [value]="true" formControlName="madeArtificialInsemination">
                  <label for="yes">Yes</label>
                </div>
                <div class="raddiv">
                  <input type="radio" name="madeArtificialInsemination" id="no" class="radio" [value]="false" formControlName="madeArtificialInsemination">
                  <label for="no">No</label>
                </div>
              </div>
            </div>

            <div class="frame-7">
              <div class="text-wrapper-6">Weight</div>
              <input class="data-filled" placeholder="Weight" id="weight" formControlName="weight" type="number" step="0.1">
            </div>
          </div>

          <div class="row">
            <div class="frame-7">
              <div class="text-wrapper-6">Date of Artificial Insemination</div>
              <input class="data-filled" type="date" id="dateFertilization" formControlName="dateFertilization">
            </div>

            <div class="frame-7">
              <div class="text-wrapper-6">Date of weight</div>
              <input class="data-filled" type="date" id="weightDate" formControlName="weightDate">
            </div>
          </div>
          
          <div class="row">
            <div class="frame-7">
              <div class="text-wrapper-6">Status of Insemination</div>
              <div class="frame-12">
                <div class="raddiv">
                  <input type="radio" name="inseminationStatus" id="pregnant" class="radio" value="Pregnant" formControlName="inseminationStatus">
                  <label for="pregnant">Pregnant</label>
                </div>
                <div class="raddiv">
                  <input type="radio" name="inseminationStatus" id="make-insemination-again" class="radio" value="Make Insemination again" formControlName="inseminationStatus">
                  <label for="make-insemination-again">Make Insemination again</label>
                </div>
              </div>
            </div>

            <div class="frame-7">
              <div class="text-wrapper-6">Expected Date of Calfing</div>
              <input class="data-filled" type="date" id="expectedDate" formControlName="expectedDate" readonly>
            </div>
          </div>

          <div class="row">
            <div class="frame-7">
              <div class="text-wrapper-6">Health care notes</div>
              <textarea class="textarea" placeholder="Health Care" id="healthCareNotes" formControlName="healthCareNotes"></textarea>
            </div>

            <div class="frame-7">
              <div class="text-wrapper-6">Taken Vaccination</div>
              <textarea class="textarea" placeholder="Taken Vaccination" id="takenVaccination" formControlName="takenVaccination"></textarea>
            </div>
          </div>

          <div class="row">
            <div class="frame-7"></div>
            <div class="frame-7">
              <button class="frame-79" type="submit" [disabled]="isLoading">
                <img class="update" src="icons/save.png" alt="Save Icon">
                <div class="btntext">{{ isLoading ? 'Saving...' : 'Save Update' }}</div>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
