.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-container {
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease;
  padding: 30px;
  box-sizing: border-box;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 20px 0;
  border-bottom: none;
}

.modal-title {
  margin: 0;
  font-size: 24px;
  color: #3d790f;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-title i {
  font-size: 28px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background-color: #f5f5f5;
  color: #ff5252;
}

.modal-body {
  padding: 20px 0 0 0;
}

.form-row-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 0;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

input[type="text"],
input[type="number"],
input[type="date"],
textarea {
  width: 100%;
  padding: 12px 15px;
  background-color: #FFFFFFCC;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

textarea {
  resize: vertical;
  min-height: 80px;
}

input:focus, textarea:focus {
  outline: none;
  border-color: #aedf32;
  box-shadow: 0 0 0 2px rgba(174, 223, 50, 0.2);
}

input.invalid, textarea.invalid {
  border-color: #ff5252;
}

.error-message {
  color: #ff5252;
  font-size: 12px;
  margin-top: 5px;
}

.main-error {
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: 4px;
  text-align: center;
}

.form-group-radio {
  margin-bottom: 20px;
}

.form-group-radio label {
  margin-bottom: 8px;
}

.gender-radio-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
}

.gender-radio-wrapper label {
  margin-bottom: 0;
}

.radio-group {
  display: flex;
  background-color: #FFFFFFCC;
  border-radius: 8px;
  overflow: hidden;

}

.radio-label {
  flex: 1;
  text-align: center;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-right: 1px solid #e0e0e0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.radio-label:last-child {
  border-right: none;
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-label input[type="radio"]:checked + span {
  background-color: #aedf32;
  color: white;
}

/* .radio-label input[type="radio"]:checked {
} */

.radio-label input[type="radio"]:checked + .radio-text {
  background-color: #aedf32;
  color: white;
}

.radio-label:has(input[type="radio"]:checked) {
  background-color: #aedf32;
  color: white;
  border-color: #aedf32;
}

.radio-label:hover {
  background-color: #e0e0e0;
}

.radio-label:has(input[type="radio"]:checked):hover {
  background-color: #aedf32;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.submit-btn {
  background-color: #32CD32;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 15px 30px;
  font-size: 18px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  max-width: 300px;
  font-weight: 600;
}

.submit-btn:hover {
  background-color: #28A745;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.scrollable-select {
  display: none;
}

.cancel-btn {
  display: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@media (max-width: 768px) {
  .form-row-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .modal-container {
    width: 95%;
    padding: 20px;
  }
}
