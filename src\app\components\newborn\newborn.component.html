<main class="content-container">
  <div class="frame-4">
    <!-- Updating Data in Newborn Button -->
    <button class="frame-9" (click)="openEditModal(null)">
      <img
        class="btn-icon"
        src="assets/images/icons/iconupdate.png"
        alt="Update Icon"
      />
      <div class="btntext">Updating Data</div>
    </button>

    <!-- Search Input -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <img
          class="search-icon"
          src="assets/images/icons/search.png"
          alt="Search Icon"
        />
        <input
          type="text"
          class="search-field"
          placeholder="Search"
          [(ngModel)]="searchTerm"
          (input)="searchNewborns()"
        />
      </div>
    </div>

    <!-- Filter Button -->
    <div
      class="filter-dropdown-container"
      [class.active]="isFilterDropdownOpen"
    >
      <button class="frame-9 filter-button" (click)="toggleFilterDropdown()">
        <img class="double-left1" src="assets/images/icons/filter.png" />
        <div class="btntext">Filter</div>
      </button>
      <div class="filter-dropdown-menu">
        <div class="filter-option" (click)="applyFilter('herdNumber')">
          Herd Number
        </div>
        <div class="filter-option" (click)="applyFilter('weight')">Weight</div>
        <div class="filter-option" (click)="applyFilter('gender')">Gender</div>
      </div>
    </div>

    <!-- Delete Animal Button -->
    <button class="frame-9 delete-btn" (click)="openDeleteModal()">
      <img
        class="btn-icon"
        src="assets/images/icons/Trash.png"
        alt="Delete Icon"
      />
      <div class="btntext">Delete Animal</div>
    </button>
  </div>

  <!-- Newborn Table -->
  <div class="table-container">
    <div class="x">
      <img class="btn-icon" src="assets/images/icons/newborn.png" alt="" />
      <h3>NewBorn</h3>
    </div>

    <table>
      <thead>
        <tr>
            <th>
            <input
              type="checkbox"
              [checked]="
                selectedAnimalIds.length === filteredNewborns.length &&
                filteredNewborns.length > 0
              "
              (change)="toggleAllAnimalSelections($event)"
            />
          </th>
          <th>Code</th>
          <th>Herd Number</th>
          <th>Gender</th>
          <th>Date Of Birth</th>
          <th>Weight</th>
          <th>Date of weight</th>
          <th>Healthcare</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let Animal of newborns">
           <td>
            <input
              type="checkbox"
              [checked]="selectedAnimalIds.includes(Animal.id!)"
              (click)="$event.stopPropagation()"
              (change)="toggleAnimalSelection(Animal.id!)"
            />
          </td>
          <td>{{ Animal.code }}</td>
          <td>{{ Animal.noFamily }}</td>
          <td>{{ genderLabels[Animal.gender] }}</td>
          <td>{{ Animal.dateOfBirth }}</td>
          <td>{{ Animal.weight }}</td>
          <td>{{ Animal.weightDate }}</td>
          <td>{{ Animal.description }}</td>
        </tr>
      </tbody>
    </table>
  </div>

 <!-- ✅ Form لتعديل بيانات Newborn -->
<div *ngIf="showEditModal">
  <div class="frame-4 modal add-edit-modal">
    <button class="close-btn" (click)="closeModals()">
      <img src="assets/images/icons/cross.png" alt="Close" />
    </button>

    <div class="frame-5">
      <img class="modal-icon" src="assets/images/icons/update.png" alt="Update Icon" />
      <div class="text-wrapper-5">Update Newborn Data</div>
    </div>

    <div class="frame-6">
      <!-- البحث عن الحيوان بالكود -->
      <div *ngIf="!selectedNewborn.code" class="form-grid">
        <div class="form-group full-width">
          <div class="text-wrapper-6">Code</div>
          <input
            type="text"
            class="data-filled"
            placeholder="Enter Code"
            [(ngModel)]="searchCode"
            (keyup.enter)="findAnimalByCode()"
          />
          <button class="frame-9 modal-save-btn" (click)="findAnimalByCode()">
            <div class="btntext">ShowData</div>
          </button>
        </div>
      </div>

      <!-- عرض باقي الفورم بعد العثور على الحيوان -->
      <div *ngIf="selectedNewborn.code" class="form-grid">
        <div class="form-group">
          <label class="text-wrapper-6">Code</label>
          <input type="text" class="data-filled" [(ngModel)]="selectedNewborn.code" />
        </div>

        <div class="form-group">
          <label class="text-wrapper-6">Weight</label>
          <input type="number" class="data-filled" [(ngModel)]="selectedNewborn.weight" />
        </div>

        <div class="form-group">
          <label class="text-wrapper-6">Date of Weight</label>
          <input type="date" class="data-filled" [(ngModel)]="selectedNewborn.weightDate" />
        </div>

        <div class="form-group">
          <label class="text-wrapper-6">Herd Number</label>
          <input type="text" class="data-filled" [(ngModel)]="selectedNewborn.noFamily" />
        </div>

        <div class="form-group">
          <label class="text-wrapper-6">Gender</label>
          <div class="radio-group">
            <div class="raddiv">
              <input type="radio" [value]="0" [(ngModel)]="selectedNewborn.gender" />
              <label>Female</label>
            </div>
            <div class="raddiv">
              <input type="radio" [value]="1" [(ngModel)]="selectedNewborn.gender" />
              <label>Male</label>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="text-wrapper-6">Vaccinations</label>
          <input type="text" class="data-filled" [(ngModel)]="selectedNewborn.description" />
        </div>

        <div class="form-group">
          <label class="text-wrapper-6">Date of Birth</label>
          <input type="date" class="data-filled" [(ngModel)]="selectedNewborn.dateOfBirth" />
        </div>

        <div class="form-group">
          <label class="text-wrapper-6">Healthcare Note</label>
          <textarea class="textarea" [(ngModel)]="selectedNewborn.description"></textarea>
        </div>

        <!-- زر الحفظ -->
        <div class="frame-8">
          <button class="frame-9 modal-save-btn" (click)="updateNewborn()">
            <img class="btn-icon" src="assets/images/icons/save.png" alt="Save Icon" />
            <div class="btntext">Save Updates</div>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

  <!-- Delete Animal Modal -->
  <div class="choose-update-milch" *ngIf="showDeleteModal">
    <div class="frame-4 modaldelete">
      <div class="delete-content">
        <img class="delete-icon" src="assets/images/icons/delete.png" alt="" />
        <div class="delete-text">Delete Animal?</div>
        <div class="delete-buttons">
          <button class="delete-cancel-btn" (click)="closeModals()">
            Cancel
          </button>
          <button class="delete-confirm-btn" (click)="confirmDeleteNewborns()">
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</main>
