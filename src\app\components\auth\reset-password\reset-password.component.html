<div class="auth-container">
  <div class="auth-content">
    <div class="auth-form-container">
      <div class="logo">
        <img src="assets/images/logo.svg" alt="SRA Logo">
      </div>

      <h2 class="auth-title">Reset Password</h2>

      <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
        <p class="form-description">Enter your new password below.</p>

        <div class="form-group">
          <label for="password">
            <i class="fas fa-lock"></i> New Password
          </label>
          <div class="password-input-container">
            <input
              [type]="showPassword ? 'text' : 'password'"
              id="password"
              formControlName="password"
              placeholder="Enter your new password"
              [ngClass]="{'invalid': resetPasswordForm.get('password')?.invalid && resetPasswordForm.get('password')?.touched}"
            >
            <button
              type="button"
              class="password-toggle"
              (click)="togglePasswordVisibility()"
              [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'"
            >
              <i class="fas" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
            </button>
          </div>
          <div class="error-message" *ngIf="resetPasswordForm.get('password')?.invalid && resetPasswordForm.get('password')?.touched">
            <span *ngIf="resetPasswordForm.get('password')?.errors?.['required']">Password is required</span>
            <span *ngIf="resetPasswordForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword">
            <i class="fas fa-lock"></i> Confirm Password
          </label>
          <div class="password-input-container">
            <input
              [type]="showConfirmPassword ? 'text' : 'password'"
              id="confirmPassword"
              formControlName="confirmPassword"
              placeholder="Confirm your new password"
              [ngClass]="{'invalid': (resetPasswordForm.get('confirmPassword')?.invalid || resetPasswordForm.hasError('mismatch')) && resetPasswordForm.get('confirmPassword')?.touched}"
            >
            <button
              type="button"
              class="password-toggle"
              (click)="toggleConfirmPasswordVisibility()"
              [attr.aria-label]="showConfirmPassword ? 'Hide password' : 'Show password'"
            >
              <i class="fas" [ngClass]="showConfirmPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
            </button>
          </div>
          <div class="error-message" *ngIf="(resetPasswordForm.get('confirmPassword')?.invalid || resetPasswordForm.hasError('mismatch')) && resetPasswordForm.get('confirmPassword')?.touched">
            <span *ngIf="resetPasswordForm.get('confirmPassword')?.errors?.['required']">Confirm password is required</span>
            <span *ngIf="resetPasswordForm.hasError('mismatch')">Passwords do not match</span>
          </div>
        </div>

        <div class="error-message main-error" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>

        <div class="success-message" *ngIf="successMessage">
          {{ successMessage }}
        </div>

        <button type="submit" class="submit-btn" [disabled]="isLoading || !token">
          <i class="fas fa-spinner fa-spin" *ngIf="isLoading"></i>
          {{ isLoading ? 'Resetting...' : 'Reset Password' }}
        </button>

        <div class="auth-footer">
          <p>Remember your password? <a routerLink="/login" class="login-link">Back to Login</a></p>
        </div>
      </form>
    </div>

    <div class="auth-image">
      <!-- Image will be set as background in CSS -->
    </div>
  </div>
</div>
