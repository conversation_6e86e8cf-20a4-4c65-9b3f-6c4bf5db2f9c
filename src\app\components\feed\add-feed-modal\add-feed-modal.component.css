.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: #ffffff;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 900px; /* Increased max-width for more fields */
  max-height: 90vh;
  overflow-y: auto;
  padding: 0;
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f7f7f7;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  position: relative;
}

.modal-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
}

.modal-title {
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #a0a0a0;
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 5px;
  border-radius: 50%;
  transition: color 0.3s ease, background-color 0.3s ease;
}

.close-btn:hover {
  color: #ff5252;
  background-color: #f0f0f0;
}

.modal-body {
  padding: 30px;
}

.form-row-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Flexible columns */
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative; /* For placeholder styling */
}

.form-group label {
  position: absolute;
  top: -10px; /* Adjust as needed for the placeholder */
  left: 15px;
  background: white;
  padding: 0 5px;
  font-size: 12px;
  color: #888;
  pointer-events: none;
  transition: all 0.2s ease-out;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
}

.form-group input,
.form-group textarea,
.form-group .custom-dropdown-container { /* Target the custom dropdown */
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  font-size: 15px;
  color: #333;
  background-color: #f9f9f9;
  transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
  box-sizing: border-box;
}

/* Placeholder styling for inputs (match image) */
.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #b0b0b0;
  font-weight: 400;
}

/* When input is focused or has content, bring label up */
.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label,
.form-group .custom-dropdown-container.has-value + label, /* For dropdowns with selected value */
.form-group .custom-dropdown-container.is-open + label { /* For open dropdowns */
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  top: -10px;
  font-size: 12px;
  color: #6a6a6a; /* Darker grey for active label */
}

/* Highlighted inputs (Protein Percentage, TDN percentage) */
.form-group input[id="proteinPercentage"],
.form-group input[id="tdnPercentage"] {
  background-color: #e0ffe0; /* Light green background */
  border-color: #a0d0a0; /* Slightly darker green border */
}

.read-only-input {
  cursor: not-allowed; /* Change cursor to indicate it's not editable */
  opacity: 0.9; /* Slightly dim it to show it's read-only */
}

.form-group input:focus,
.form-group textarea:focus,
.form-group .custom-dropdown-container:focus-within { /* For custom dropdown focus */
  outline: none;
  border-color: #8bc34a; /* Green focus border */
  box-shadow: 0 0 0 3px rgba(139, 195, 74, 0.2);
  background-color: #ffffff;
}

.form-group input.invalid,
.form-group textarea.invalid,
.form-group .custom-dropdown-container.invalid .dropdown-toggle {
  border-color: #ff5252;
  box-shadow: 0 0 0 3px rgba(255, 82, 82, 0.2);
}

.error-message {
  color: #ff5252;
  font-size: 12px;
  margin-top: 5px;
}

/* Specific styling for custom dropdowns placeholders and selected text */
/* This might need adjustment based on custom-dropdown.component.css */
.custom-dropdown-container .placeholder,
.custom-dropdown-container .selected-option-text {
  color: #b0b0b0; /* Match input placeholder color */
}

.custom-dropdown-container.has-value .selected-option-text {
  color: #333; /* Darker text when a value is selected */
}

/* Ingredients Section */
.ingredients-section {
  margin-top: 30px;
  margin-bottom: 25px;
}

.ingredient-selection-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 50px; /* Three flexible columns for inputs, one fixed for button */
  gap: 20px;
  align-items: flex-end; /* Align all items in the row to the bottom vertically */
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 10px; /* Space between price input and add button */
}

.price-input-group .price-input {
  flex-grow: 1;
}

.add-button-container {
  display: flex;
  justify-content: flex-end; /* Align the button to the right of its container */
  align-items: flex-end; /* Align the button to the bottom of its container */
}

.add-ingredient-btn {
  background-color: #8bc34a;
  color: white;
  border: none;
  border-radius: 10px;
  width: 50px;
  height: 50px;
  min-width: 50px;
  min-height: 50px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  font-size: 24px;
  box-shadow: 0 4px 10px rgba(139, 195, 74, 0.3);
}

.add-ingredient-btn:hover {
  background-color: #7cb342;
  transform: translateY(-2px);
}

.add-ingredient-btn i {
  font-size: 24px;
}

.no-ingredients {
  padding: 20px;
  text-align: center;
  color: #888;
  font-style: italic;
  background-color: #f0f0f0;
  border-radius: 10px;
  margin-top: 20px;
}

.ingredients-list {
  margin-top: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  background-color: #ffffff;
}

.ingredient-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 15px;
}

.ingredient-row:last-child {
  border-bottom: none;
}

.ingredient-info {
  display: flex;
  gap: 15px;
  color: #555;
}

.ingredient-name {
  font-weight: 500;
  color: #333;
}

.ingredient-price-value {
  font-weight: 500;
  color: #6a6a6a;
}

.remove-btn {
  background: none;
  border: none;
  color: #ff5252;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.remove-btn:hover {
  background-color: #ffe0e0;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.finish-ingredients-text {
  display: flex;
  align-items: center;
  color: #8bc34a; /* Green color from image */
  font-size: 16px;
  font-weight: 500;
  position: relative;
  padding-bottom: 5px; /* Space for underline */
}

.finish-ingredients-text::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: #8bc34a; /* Green underline */
}

.finish-ingredients-text img {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}

.submit-btn {
  background-color: #6cb628; /* Darker green for the button */
  color: white;
  border: none;
  border-radius: 12px; /* More rounded button */
  padding: 15px 30px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 8px 15px rgba(108, 182, 40, 0.3);
}

.submit-btn:hover {
  background-color: #5aa121;
  box-shadow: 0 10px 20px rgba(108, 182, 40, 0.4);
}

.submit-btn:disabled {
  background-color: #b0b0b0;
  cursor: not-allowed;
  box-shadow: none;
}

.submit-btn .save-icon {
  width: 24px;
  height: 24px;
}

/* Animations (keeping existing ones if they are relevant) */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
