.auth-container {
  display: flex;

 height: 100vh;
 margin: 0;
justify-content: space-around;

  background-color: #E3E4E4;;
  padding: 20px;


}


.unsplash{
  position: absolute;

}
.auth-image .unsplash{
height: 100%;
width: 44%;
top: 0px;
right: 0px;
}
.auth-image .unsplash img
{
object-fit: fill;
}

.auth-content {
  display: flex;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;

  border-radius: 10px;

}


.auth-form-container {
 display: flex;
    flex-direction: column;
    width: 60%;
    align-items: flex-start;
    gap:21px;
    position: relative;
    flex: 0 0 auto;
}

.logo {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 80px;
}

.logo img {
  height: 50px;
  object-fit: contain;
  position: fixed;
  top:1;
  left: 0;
}

.auth-title {
  font-size: 30px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  position: fixed;
  top:20%;
  left: 10%;
  flex: 1;
  margin-bottom: 45px;
}

.form-group {
  margin-bottom: 20px;
  margin: 20px;

}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 24px;
}

label i {
  margin-right: 5px;
  color: #32CD32;
}

input[type="email"],
input[type="password"],
input[type="text"] {
  width:423px;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 12px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

input:focus {
  outline: none;
  border-color: #8bc34a;
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.2);
}

input.invalid {
  border-color: #ff5252;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 5px;
}

.password-toggle:hover {
  color: #333;
}

.error-message {
  color: #ff5252;
  font-size: 12px;
  margin-top: 5px;
}

.main-error {
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: 4px;
  text-align: center;
}

.form-actions {
  margin-bottom: 20px;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me input[type="checkbox"] {
  margin-right: 5px;
}

.forgot-password {
  color: #717171;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.submit-btn {
  background-color: #32CD32;;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
  width: 423px;
  height: 70px;
  margin: 20px;

}

.submit-btn:hover {
  background-color: #5eeb5e;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.auth-footer {
  margin-top: 30px;
  text-align: center;
  font-weight: 500px;
  font-size: 18px;
  color: #666;
}

.login-link {
  color: #009400;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
}

.register-link:hover {
  text-decoration: underline;
}

.auth-image {
  flex: 1;
  height: 400px;

  background-size: cover;
  background-position: center;
  display: none;
}
.auth-image{
  height: 400px;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .auth-image {
    display: block;
  }
}

@media (max-width: 767px) {
  .auth-content {
    max-width: 400px;
  }

  .auth-form-container {
    padding: 30px 20px;
  }
}
