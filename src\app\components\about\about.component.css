.frame-child7 {
  position: absolute;
  top: 950px;
  left: 155px;
  width: 744px;
  height: 112px;
  display: none;
}

.unsplash7cyehjgcosi-icon {
  /* margin-top: -724px;
  position: absolute;
  top: -68px;
  left: 3px;*/
  border-radius: var(--br-7xs);
  width: 100%;
  height: 45%;
  object-fit: fill;
  flex-shrink: 0;
}

.about-us1 {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 500;

  font-family: inherit;
}

.about-us-title {
  /* width: 309px; */
  height: 39px;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
  padding: 0 10px;
  box-sizing: border-box;
}

/* .we-will {
  margin: 0;
  /* white-space: pre-wrap;
} */
.this-website-help {
  margin: 0;
}

.we-will-help-container {
  width: 100%;
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}

.about-us-description,
.about-us-title-parent {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.about-us-description {
  width: 100%;
  flex-direction: row;
  padding-left: 10px;
  box-sizing: border-box;
  font-size: 26px ;
  color: var(--color-gray-400);
  font-family: var(--highlights);
}

.about-us-title-parent {
  align-self: stretch;
  flex-direction: column;
  padding-bottom: 6px;
}

.who-we-are {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
}

.we-are-a-container {
  width: 880px;
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}

.we-are-a-team-try-to-make-life-wrapper {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  font-size: 17px;
  color: var(--color-gray-400);
  font-family: var(--highlights);
}

.frame-parent5,
.frame-parent6 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  max-width: 100%;
}

.frame-parent6 {
  width: 100%;
  flex: 1;
  align-items: flex-start;
  font-size: 40px;
  padding-left: 18px;
}

/* .frame-parent5 {
  align-self: stretch;
  width: 765px;
  align-items: flex-end;
  flex-shrink: 0;
} */
/* .footer,
.frame-wrapper4 {
  position: absolute;
} */
.frame-wrapper4 {
  width: 100%;
  height: 30%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  max-width: 100%;
  padding-top: 10px;
  padding-left: 60px;
  background-color: #ffffff;

}

.footer {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  background-color:#0b291a;
  width: 100%;
  height: 31%;
  color: #fff;

}

.social-media-icon {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: contain;
}

.social {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
}

.e-socoal {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px;
  gap: 6px;
}

.facebook-icon {
  height: 45px;
  width: 45;

  position: relative;

}

.facebook-parent {

  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;

}

.e-socoal-parent {
  height: 100%px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  box-sizing: border-box;
  min-width: 310px;
  justify-content: center;
}

.connect,
.phone-email-labels {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.connect {

  padding: 10px 4px 0;
  gap: 10px;
  font-size: 23px;
  box-sizing: border-box;
}

.phone-email-labels {
  padding: 0 16px;
}

.email-info {
  position: relative;
  padding: 4px 0;
}

.email1,
.phone {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.email1 {
  gap: 26px;
}



.connect-us,
.frame-parent7,
.hours {
  display: flex;
  justify-content: space-around;
}

.hours {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 23px;
}

.connect-us,
.frame-parent7 {
  max-width: 100%;
}

.connect-us {
  height: 100%;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;
  font-size: 15px;
}

.frame-parent7 {
  width: 100%;
  flex-direction: row;
  align-items: center;
  font-size: 23px;
  color: var(--color-white);
}

.frame-parent4 {
  height: 101%;
  flex: 1;
  position: relative;
  border-radius:16px;
  background-color: var(--color-white);
  overflow: hidden;
  max-width: 92%;

  box-shadow: 0 4px 8px rgba(11, 41, 26, 0.1);
  /* إضافة ظل خفيف */
}

.frame-parent4::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 44%;
  background: linear-gradient(to bottom, rgba(42, 82, 56, 0.3), rgba(42, 82, 56, 0));
  z-index: 1;
  pointer-events: none;
}

.frame-wrapper3 {
  position: absolute;
  width: 82%;
  height: 89%;
  top: 10%;
  left: 18%;
  align-self: stretch;
  flex-direction: row;
  /* padding: 0 25px 0; */
  box-sizing: border-box;

  border-radius: 16px;
}


.frame-wrapper3 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}


.about-us {
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
   position: absolute;
  background-color: #e3e4e4;
  overflow: hidden;
  flex-direction: row;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  /* font-family: var(--font-abeezee); */
}


@media screen and (max-width: 1350px) {
  .about-us {
    flex-wrap: wrap;
  }

  .frame-parent4 {
    width: 100%;
  }

  .unsplash7cyehjgcosi-icon {
    width: 100%;
    max-width: 1007px;
  }

  .footer {
    width: 100%;
    max-width: 1007px;
  }

  .frame-parent7 {
    width: 100%;
    max-width: 939px;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    gap: 40px;
  }
}

@media screen and (max-width: 1150px) {
  .about-us1 {
    font-size: 51px;
  }

  .who-we-are {
    font-size: 43px;
  }

  .social {
    font-size: var(--font-size-5xl);
  }

  .e-socoal-parent {
    flex: 1;
  }

  .connect-us {
    flex: 1;
    padding-right: 0;
    box-sizing: border-box;
  }

  .frame-parent7 {
    flex-wrap: wrap;
    justify-content: center;
    position: absolute;
    top: 545px;
  }

  .frame-parent4 {
    height: auto;
    min-height: 705px;
  }

  .about-us-inner {
    max-width: 100%;
  }

  .about-us {
    padding-left: var(--padding-base);
    padding-right: var(--padding-base);
    box-sizing: border-box;
  }

  .we-will-help-container,
  .we-are-a-container {
    width: 100%;
    max-width: 736px;
  }

  .frame-wrapper4 {
    width: 100%;
    max-width: 896px;
    position: relative;
    top: 296px;
    left: 0;
    right: 0;
    margin: 0 auto;
    height: auto;
  }

  .footer {
    position: relative;
    top: 300px;
    left: 0;
    right: 0;
    margin: 0 auto;
  }
}

@media screen and (max-width: 900px) {
  .side-bar {
    width: 250px;
  }


  .navbar {
    width: 100%;
  }

  .about-us1 {
    font-size: 45px;
  }

  .who-we-are {
    font-size: 35px;
  }

  .we-will-help-container,
  .we-are-a-container {
    font-size: 16px;
  }

  .frame-parent7 {
    gap: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    position: absolute;
    top: 545px;
  }

  .e-socoal-parent,
  .connect-us {
    min-width: 280px;
  }

  .unsplash7cyehjgcosi-icon {
    height: 300px;
  }
}

@media screen and (max-width: 800px) {
  .connect-us {
    min-width: 100%;
  }


  .about-us1 {
    font-size: 40px;
  }

  .who-we-are {
    font-size: 32px;
  }

  .frame-wrapper4 {
    height: auto;
  }

  .frame-parent5 {
    width: 100%;
    max-width: 765px;
  }

  .frame-parent6 {
    width: 100%;
    max-width: 749px;
  }

  .e-socoal,
  .facebook-parent,
  .phone,
  .email1,
  .hours {
    width: 100%;
  }

  .frame-parent7 {
    position: absolute;
    top: 545px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 90%;
  }
}

@media screen and (max-width: 700px) {
  .about-us {
    flex-direction: column;
  }

  .side-bar {
    width: 100%;
    height: auto;
    margin-bottom: 20px;
  }


  .frame-parent4 {
    margin-top: 20px;
  }

  .unsplash7cyehjgcosi-icon {
    position: relative;
    top: 0;
    height: 250px;
    background-color: #2a5238;
    /* تحديث لون الخلفية في الشاشات الصغيرة */
  }

  .frame-wrapper4 {
    position: relative;
    top: 20px;
  }

  .footer {
    position: relative;
    top: 40px;
    height: auto;
    min-height: 157px;
  }

  .frame-parent7 {
    position: absolute;
    top: 545px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 90%;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-start;
  }

  .e-socoal-parent,
  .connect-us {
    width: 45%;
    margin-bottom: 20px;
  }

  .s-r-a-parent-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .side-bar-bottom-inner {
    width: 100%;
  }

  .side-bar-elements {
    width: 100%;
    height: auto;
  }

  .side-bar-options {
    width: 100%;
    height: auto;
  }

  .side-bar-element {
    width: 100%;
    gap: 20px;
  }

  .side-animal,
  .side-pregnant,
  .side-newborn,
  .side-feed,
  .side-ingredients,
  .side-report {
    width: auto;
  }

  .e-socoal {
    width: 100%;
    justify-content: center;
  }

  .facebook-parent {
    justify-content: center;
    gap: 10px;
  }

  .connect {
    width: 100%;
    justify-content: center;
  }

  .phone,
  .email1,
  .hours {
    width: 100%;
    justify-content: center;
  }

  .phone-email-labels {
    justify-content: center;
  }

  .email-info {
    width: auto;
    text-align: center;
  }
}

@media screen and (max-width: 600px) {
  .frame-parent7 {
    position: absolute;
    top: 545px;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
  }

  .e-socoal-parent,
  .connect-us {
    width: 100%;
    margin-bottom: 10px;
  }
}

@media screen and (max-width: 450px) {
  .about-us1 {
    font-size: 32px;
  }

  .we-will-help-container {
    font-size: var(--font-size-base);
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
  }

  .who-we-are {
    font-size: var(--highlights-size);
  }

  .we-are-a-container {
    font-size: var(--font-size-base);
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
  }

  .social {
    font-size: var(--font-size-lg);
  }

  .e-socoal,
  .facebook-parent {
    flex-wrap: wrap;
  }

  .e-socoal-parent {
    height: auto;
    min-height: 170px;
  }

  .email-info {
    font-size: var(--font-size-base);
  }

  .email1,
  .phone {
    flex-wrap: wrap;
  }

  .connect-us {
    height: auto;
  }

  .unsplash7cyehjgcosi-icon {
    height: 200px;
  }

  .navbar {
    height: auto;
    padding: 5px;
  }

  .notification-bell-icon,
  .male-avatar-portrait-of-a-youn-icon {
    height: 30px;
    width: 30px;
  }

  .facebook-icon {
    max-width: 60px;
    min-width: 50px;
  }

  .about-us-description,
  .we-are-a-team-try-to-make-life-wrapper {
    padding: 0 5px;
  }

  .side-bar-bottom {
    height: auto;
  }

  .side-bar-element {
    height: auto;
    padding: 10px 0;
  }

  .frame-parent7 {
    position: absolute;
    top: 545px;
  }
}

@media screen and (max-width: 350px) {
  .about-us1 {
    font-size: 28px;
  }

  .who-we-are {
    font-size: 24px;
  }

  .we-will-help-container,
  .we-are-a-container {
    font-size: 14px;
  }

  .social {
    font-size: 18px;
  }

  .email-info {
    font-size: 14px;
  }

  .animals {
    font-size: 20px;
  }

  .vuesaxlinearcategory-2-icon,
  .bull-icon,
  .cow-1-icon {
    height: 25px;
    width: 25px;
  }

  .social-media-icon {
    height: 30px;
    width: 50px;
  }

  .facebook-icon {
    height: 35px;
    max-width: 50px;
    min-width: 40px;
  }

  .connect {
    font-size: 18px;
  }

  .frame-parent7 {
    gap: 10px;
    position: absolute;
    top: 545px;
  }
}


/* إعادة تصميم قائمة النافبار المنسدلة */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 100;
}

.nav-profile1 {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 140px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 101;
}

.component-13,
.component-131,
.component-14 {
  align-self: stretch;
  height: 44px;
  border-radius: 6px;
  background-color: #fbfaf0;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 11px 10px;
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-13:hover,
.component-131:hover,
.component-14:hover {
  background-color: #f0f0f0;
}

.log-out,
.log-out1,
.log-out2 {
  width: auto;
  position: relative;
  font-weight: 500;
  display: inline-block;
}

.popup-overlay a {
  text-decoration: none;
  color: black;
}

/* إضافة مؤشر للعنصر القابل للنقر */
#navProfileContainer {
  cursor: pointer;
}

/* تصغير حجم الخط والأيقونات في صفحة about-us */

/* تصغير حجم الخط في العناوين الرئيسية */
.about-us1 {
  font-size: 40px;
  /* تقليل أكثر من 40px */
  font-weight: 400;
}

.who-we-are {
  font-size: 40px;
  /* تقليل أكثر من 35px */
  font-weight: 400;
  padding-bottom: 5px;
}

/* تصغير حجم الخط في النصوص */
.we-will-help-container,
.we-are-a-container {
  font-size: 18px;
  /* تقليل أكثر من 15px */
  line-height: 1.5;
  text-align: left;

}

/* تصغير حجم الخط في الفوتر */
.social {
  font-size: 28px;
  /* تقليل أكثر من 20px */
}

.connect {
  font-size: 18px;
  /* تقليل أكثر من 20px */
}

.email-info {
  font-size: 16px;
  margin-top: 3px;
  /* تقليل أكثر من 14px */
}

/* تصغير حجم الأيقونات في الفوتر */
.social-media-icon {
  height: 35px;
  /* تقليل أكثر من 40px */
  width: 55px;
  /* تقليل أكثر من 65px */
}

.facebook-icon {
  height: 35px;
  /* تقليل أكثر من 40px */
  max-width: 55px;
  /* تقليل أكثر من 65px */
  min-width: 50px;
  /* تقليل أكثر من 60px */
}

/* تعديل الاستجابة للشاشات المختلفة */
@media screen and (max-width: 1150px) {
  .about-us1 {
    font-size: 34px;
    /* تقليل أكثر من 45px */
  }

  .who-we-are {
    font-size: 28px;
    /* تقليل أكثر من 38px */
  }

  .social {
    font-size: 16px;
    /* تقليل أكثر من 18px */
  }
}

@media screen and (max-width: 900px) {
  .about-us1 {
    font-size: 32px;
    /* تقليل أكثر من 40px */
  }

  .who-we-are {
    font-size: 26px;
    /* تقليل أكثر من 32px */
  }

  .we-will-help-container,
  .we-are-a-container {
    font-size: 13px;
    /* تقليل أكثر من 14px */
  }
}

@media screen and (max-width: 700px) {
  .about-us1 {
    font-size: 30px;
    /* تقليل أكثر من 35px */
  }

  .who-we-are {
    font-size: 24px;
    /* تقليل أكثر من 28px */
  }
}

@media screen and (max-width: 450px) {
  .about-us1 {
    font-size: 24px;
    /* تقليل أكثر من 28px */
  }

  .we-will-help-container {
    font-size: 12px;
    /* تقليل أكثر من 13px */
  }

  .who-we-are {
    font-size: 20px;
    /* تقليل أكثر من 22px */
  }

  .we-are-a-container {
    font-size: 12px;
    /* تقليل أكثر من 13px */
  }

  .social {
    font-size: 15px;
    /* تقليل أكثر من 16px */
  }

  .email-info {
    font-size: 12px;
    /* تقليل أكثر من 13px */
  }

  .social-media-icon {
    height: 30px;
    /* تقليل أكثر من 35px */
    width: 50px;
    /* تقليل أكثر من 55px */
  }

  .facebook-icon {
    height: 30px;
    /* تقليل أكثر من 35px */
    max-width: 50px;
    /* تقليل أكثر من 55px */
    min-width: 45px;
    /* تقليل أكثر من 50px */
  }
}

@media screen and (max-width: 350px) {
  .about-us1 {
    font-size: 22px;
    /* تقليل أكثر من 25px */
  }

  .who-we-are {
    font-size: 18px;
    /* تقليل أكثر من 20px */
  }

  .we-will-help-container,
  .we-are-a-container {
    font-size: 11px;
    /* تقليل أكثر من 12px */
  }

  .social {
    font-size: 14px;
    /* تقليل أكثر من 16px */
  }

  .email-info {
    font-size: 11px;
    /* تقليل أكثر من 12px */
  }

  .social-media-icon {
    height: 25px;
    /* تقليل أكثر من 28px */
    width: 40px;
    /* تقليل أكثر من 45px */
  }

  .facebook-icon {
    height: 25px;
    /* تقليل أكثر من 30px */
    max-width: 40px;
    /* تقليل أكثر من 45px */
    min-width: 30px;
    /* تقليل أكثر من 35px */
  }

  .connect {
    font-size: 14px;
    /* تقليل أكثر من 16px */
  }
}
