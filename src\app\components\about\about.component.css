/* Overall container styling */
.about-container {
 
  font-family: 'Arial', sans-serif;
  background-color: #f0f2f5;
  padding: 20px;

}

/* Image Section */
.about-image-section {

  background-size: contain;
  width: 100%;
  height: 400px; /* Allow height to adjust to content */
  /* max-height: 400px; Removed to allow full image display */
  overflow: hidden;
  align-items: center;
  margin-bottom: 40px;
  border-radius: 10px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
}

.about-cow-image {
  width: 100%;
  height: auto; /* Allow image to maintain its aspect ratio */
  display: block;
  background-size:contain;
  object-position: center; /* Center the image entirely */
  transition: transform 0.3s ease;
}

.about-cow-image:hover {
  transform: scale(1.02);
}

/* Content Sections */
.about-content {
  background-color: #ffffff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.about-section {
  margin-bottom: 25px;
}

.about-section h2 {
  font-size: 28px;
  color: #333;
  margin-bottom: 10px;
  position: relative;
  padding-bottom: 10px;
}

.about-section h2::after {
  content: '';
  display: block;
  width: 70px;
  height: 3px;
  background-color: #4CAF50; /* Green line */
  position: absolute;
  bottom: 0;
  left: 0;
}

.about-section p {
  font-size: 16px;
  line-height: 1.6;
  color: #555;
}

/* Footer Section */
.footer-section {
  display: flex;
  justify-content: space-around;
  background-color: #2e7d32; /* Darker green background for footer */
  color: white;
  padding: 30px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.social-links,
.contact-info {
  flex: 1;
  padding: 0 20px;
}

.footer-heading {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.footer-icon {
  width: 35px;
  height: 30px;
  margin-right: 10px;
  filter: invert(100%); /* Make icons white */
}

.footer-heading h3 {
  font-size: 22px;
  margin: 0;
  color: white;
}

.social-icons a img {
  width: 40px;
  height: 40px;
  margin-right: 15px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2); /* Semi-transparent white background */
  padding: 8px;
  transition: background-color 0.3s ease;
}

.social-icons a img:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.contact-details p {
  margin-bottom: 10px;
  font-size: 16px;
}

.contact-details strong {
  font-weight: 600;
  margin-right: 5px;
}

.contact-details br {
  display: none; /* Hide default line break for finer control */
}

.contact-details p:last-child br {
  display: block; /* Show line break only for hours */
} 