<div class="modal-overlay">
  <div class="modal-container">
    <div class="modal-header">
      <h2>Add New Ingredient</h2>
      <button class="close-btn" (click)="closeModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <div class="modal-body">
      <form [formGroup]="ingredientForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <div class="form-group">
            <label for="name">Name</label>
            <input 
              type="text" 
              id="name" 
              formControlName="name" 
              placeholder="Enter ingredient name"
              [ngClass]="{'invalid': ingredientForm.get('name')?.invalid && ingredientForm.get('name')?.touched}"
            >
            <div class="error-message" *ngIf="ingredientForm.get('name')?.invalid && ingredientForm.get('name')?.touched">
              Ingredient name is required
            </div>
          </div>
          
          <div class="form-group">
            <label for="category">Category</label>
            <select 
              id="category" 
              formControlName="category"
            >
              <option value="">Select category (optional)</option>
              <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
            </select>
          </div>
        </div>
        
        <div class="form-group">
          <label for="description">Description</label>
          <textarea 
            id="description" 
            formControlName="description" 
            placeholder="Enter ingredient description (optional)"
            rows="3"
          ></textarea>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="price">Price</label>
            <input 
              type="number" 
              id="price" 
              formControlName="price" 
              placeholder="Enter price"
              [ngClass]="{'invalid': ingredientForm.get('price')?.invalid && ingredientForm.get('price')?.touched}"
            >
            <div class="error-message" *ngIf="ingredientForm.get('price')?.invalid && ingredientForm.get('price')?.touched">
              Price must be a positive number
            </div>
          </div>
          
          <div class="form-group">
            <label for="quantity">Quantity</label>
            <input 
              type="number" 
              id="quantity" 
              formControlName="quantity" 
              placeholder="Enter quantity"
              [ngClass]="{'invalid': ingredientForm.get('quantity')?.invalid && ingredientForm.get('quantity')?.touched}"
            >
            <div class="error-message" *ngIf="ingredientForm.get('quantity')?.invalid && ingredientForm.get('quantity')?.touched">
              Quantity must be a positive number
            </div>
          </div>
          
          <div class="form-group">
            <label for="unit">Unit</label>
            <select 
              id="unit" 
              formControlName="unit"
            >
              <option *ngFor="let unit of units" [value]="unit">{{ unit }}</option>
            </select>
          </div>
        </div>
        
        <div class="error-message main-error" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>
        
        <div class="form-actions">
          <button type="button" class="cancel-btn" (click)="closeModal()">Cancel</button>
          <button type="submit" class="submit-btn" [disabled]="isLoading">
            <i class="fas fa-spinner fa-spin" *ngIf="isLoading"></i>
            {{ isLoading ? 'Saving...' : 'Save Ingredient' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
