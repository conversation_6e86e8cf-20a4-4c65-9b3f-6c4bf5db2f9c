/* Basic container styling */
.settings-container {
  padding: 30px;
  background-color: #ffffff; /* White background */
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* Softer shadow */
  max-width: 800px;
  margin: 30px auto; /* Center the container with more vertical margin */
}

/* Header styling */
.settings-header {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  border-bottom: 1px solid #e0e0e0; /* Lighter border */
  padding-bottom: 20px;
}

.settings-icon {
  width: 32px;
  height: 32px;
  margin-right: 15px;
  /* The icon in the image appears to have an orange tint, assuming it's part of the image asset */
}

.settings-header h2 {
  font-size: 26px;
  color: #333; /* Darker text */
  margin: 0;
  font-weight: 600;
}

/* Section styling */
.settings-section {
  margin-bottom: 40px;
  position: relative; /* For positioning the edit button */
}

.settings-section h3 {
  font-size: 22px;
  color: #444; /* Slightly darker */
  margin-bottom: 20px;
  font-weight: 500;
}

.settings-section h3::after {
  content: '';
  display: block;
  width: 60px; /* Slightly longer line */
  height: 2px;
  background-color: #ff9800; /* Orange line under section title */
  margin-top: 8px;
}

/* Info row styling */
.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0; /* Very light border */
}

.info-row:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #777; /* Softer gray */
  flex: 1;
  padding-left: 10px; /* Indent labels slightly */
}

.value {
  color: #333; /* Darker text */
  flex: 2;
  text-align: left; /* Align value to the left, as per image */
  padding-right: 10px; /* Indent values slightly */
}

/* Profile picture styling */
.profile-picture {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #f5f5f5; /* Lighter, thicker border */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Edit button styling */
.edit-button {
  background-color: #e0e0e0; /* Light gray background */
  color: #555; /* Darker text color */
  padding: 8px 20px;
  border: none;
  border-radius: 20px; /* More rounded */
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  position: absolute;
  top: 0;
  right: 0;
}

.edit-button:hover {
  background-color: #d0d0d0;
  color: #333;
} 