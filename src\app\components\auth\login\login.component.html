<div class="auth-container">
  <div class="auth-content">
    <div class="auth-form-container">
      <div class="logo">
        <img src="assets/images/icons/logo.png" alt="">

      </div>
<div class="w">
      <h1 class="auth-title">WelcomeBack</h1>
      <img src="assets/images/icons/welcome.png" alt="">
      </div>

      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
        <div class="form-group">
          <label for="email">
            <i class="fas fa-envelope"></i> Email
          </label>
          <input
            type="email"
            id="email"
            formControlName="email"
            placeholder="Enter your email"
            [ngClass]="{'invalid': loginForm.get('email')?.invalid && loginForm.get('email')?.touched}"
          >
          <div class="error-message" *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
            <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
            <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</span>
          </div>
        </div>

        <div class="form-group">
          <label for="password">
            <i class="fas fa-lock"></i> Password
          </label>
          <div class="password-input-container">
            <input
              [type]="showPassword ? 'text' : 'password'"
              id="password"
              formControlName="password"
              placeholder="Enter your password"
              [ngClass]="{'invalid': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}"
            >
            <button
              type="button"
              class="password-toggle"
              (click)="togglePasswordVisibility()"
              [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'"
            >
              <i class="fas" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
            </button>
          </div>
          <div class="error-message" *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
            <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
            <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
          </div>
        </div>

        <div class="form-actions">
          <div class="remember-forgot">
            <div class="remember-me">
              <input type="checkbox" id="remember">
              <label for="remember">Remember me</label>
            </div>
            <a routerLink="/forgot-password" class="forgot-password">Forgot Password?</a>
          </div>
        </div>

        <div class="error-message main-error" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>

        <button type="submit" class="submit-btn" [disabled]="isLoading">
          <i class="fas fa-spinner fa-spin" *ngIf="isLoading"></i>
          {{ isLoading ? 'Logging in...' : 'Login' }}
        </button>

        <div class="auth-footer">
          <p>Don't have an account? <a routerLink="/register" class="register-link">Create Account</a></p>
        </div>
      </form>
    </div>

    <div class="auth-image">
      <!-- Image will be set as background in CSS -->
        <img class="unsplash" src="assets/images/login.png" />
    </div>
  </div>
</div>

