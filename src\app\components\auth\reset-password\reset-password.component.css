.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.auth-content {
  display: flex;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.auth-form-container {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
}

.logo {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.logo img {
  height: 40px;
  object-fit: contain;
}

.auth-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 30px;
}

.form-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.auth-form {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

label i {
  margin-right: 5px;
  color: #8bc34a;
}

input[type="email"],
input[type="password"],
input[type="text"] {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

input:focus {
  outline: none;
  border-color: #8bc34a;
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.2);
}

input.invalid {
  border-color: #ff5252;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 5px;
}

.password-toggle:hover {
  color: #333;
}

.error-message {
  color: #ff5252;
  font-size: 12px;
  margin-top: 5px;
}

.success-message {
  color: #8bc34a;
  font-size: 14px;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(139, 195, 74, 0.1);
  border-radius: 4px;
  text-align: center;
}

.main-error {
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(255, 82, 82, 0.1);
  border-radius: 4px;
  text-align: center;
}

.submit-btn {
  background-color: #8bc34a;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
}

.submit-btn:hover {
  background-color: #7cb342;
}

.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.auth-footer {
  margin-top: 30px;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.login-link {
  color: #8bc34a;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
}

.login-link:hover {
  text-decoration: underline;
}

.auth-image {
  flex: 1;
  background-image: url('/assets/images/auth-bg.svg');
  background-size: cover;
  background-position: center;
  display: none;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .auth-image {
    display: block;
  }
}

@media (max-width: 767px) {
  .auth-content {
    max-width: 400px;
  }

  .auth-form-container {
    padding: 30px 20px;
  }
}