<div class="auth-container">
  <div class="auth-content">
    <div class="auth-form-container">
      <div class="logo">
        <img src="assets/images/icons/logo.png" alt="">
      </div>



      <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
         <h2 class="auth-title">Reset Password</h2>
        <p class="form-description">We will send an email with instructions to reset your password.</p>

        <div class="form-group">
          <label for="email">
            <i class="fas fa-envelope"></i> Email
          </label>
          <input
            type="email"
            id="email"
            formControlName="email"
            placeholder="Enter your email"
            [ngClass]="{'invalid': forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched}"
          >
          <div class="error-message" *ngIf="forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched">
            <span *ngIf="forgotPasswordForm.get('email')?.errors?.['required']">Email is required</span>
            <span *ngIf="forgotPasswordForm.get('email')?.errors?.['email']">Please enter a valid email</span>
          </div>
        </div>

        <div class="error-message main-error" *ngIf="errorMessage">
          {{ errorMessage }}
        </div>

        <div class="success-message" *ngIf="successMessage">
          {{ successMessage }}
        </div>

        <button type="submit" class="submit-btn" [disabled]="isLoading">
          <i class="fas fa-spinner fa-spin" *ngIf="isLoading"></i>
          {{ isLoading ? 'Sending...' : 'Login' }}
        </button>

        <div class="auth-footer">
          <p>Remember your password? <a routerLink="/login" class="login-link">Back to Login</a></p>
        </div>
      </form>
    </div>

    <div class="auth-image">
      <!-- Image will be set as background in CSS -->
        <img class="unsplash" src="assets/images/about.png" />
    </div>
  </div>
</div>
